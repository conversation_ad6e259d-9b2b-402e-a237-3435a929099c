from pydantic import BaseModel


class ImageUrlDetail(BaseModel):
    url: str
    # detail: Optional[str] = "auto" # OpenAI vision model detail, not directly applicable here


class ContentItem(BaseModel):
    type: str
    text: str | None = None
    image_url: ImageUrlDetail | None = None  # 兼容 OpenAI Vision API 的 image_url 格式


class Message(BaseModel):
    role: str
    content: str | list[ContentItem]
    name: str | None = None


class ChatCompletionRequest(BaseModel):
    model: str
    messages: list[Message]
    temperature: float | None = 0.7
    top_p: float | None = 1.0
    stream: bool | None = False
    max_tokens: int | None = None  # Gemini Web API 不直接支持 max_tokens
    user: str | None = None
    # tools: Optional[List[Any]] = None # 预留对 OpenAI tools/function calling 的支持
    # tool_choice: Optional[Any] = None # 预留

    # --- 自定义字段，用于传递给 UserScript ---
    # 如果需要支持连续对话，可以在这里传递上一次的元数据
    # gemini_chat_metadata: Optional[List[str]] = Field(None, alias="chat_metadata")


class ModelData(BaseModel):
    id: str
    object: str = "model"
    created: int
    owned_by: str = "gemini-userscript-bridge"


class ModelList(BaseModel):
    object: str = "list"
    data: list[ModelData]
