import logging
import os

import uvicorn

from server.api import create_chat_completion, list_models, root, userscript, websocket_endpoint_userscript
from server.core import ModelList, app

logger = logging.getLogger(__name__)


# --- API Key 认证 ---
SERVER_OWN_API_KEY = os.environ.get("API_KEY")


def register_routers():
    # --- 注册FastAPI 路由 ---
    # 处理 Userscript 的 WebSocket 连接
    app.websocket("/ws/userscript")(websocket_endpoint_userscript)
    # list_models 处理 /v1/models 的 GET 请求
    app.get("/v1/models", response_model=ModelList)(list_models)

    app.post("/v1/chat/completions")(create_chat_completion)

    app.get("/")(root)

    app.get("/userscript")(userscript)


def run(host: str = "0.0.0.0", port: int = 8000):
    register_routers()

    if SERVER_OWN_API_KEY:
        logger.info(f"服务器自身 API_KEY 已通过环境变量设置 (前5位): {SERVER_OWN_API_KEY[:5]}...")
    else:
        logger.warning(
            "警告: 服务器自身 API_KEY (环境变量 API_KEY) 未设置。如果 Userscript 也未设置 API Key，则 API 将无保护。"
        )

    raise SystemExit(uvicorn.run(app, host=host, port=port, log_level="info"))
