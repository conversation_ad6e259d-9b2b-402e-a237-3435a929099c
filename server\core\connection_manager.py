import asyncio
import logging
from typing import Any

from fastapi import (
    FastAP<PERSON>,
    HTTPException,
    WebSocket,
    WebSocketDisconnect,
)
from fastapi.middleware.cors import CORSMiddleware
from fastapi.websockets import WebSocketState

logger = logging.getLogger(__name__)


# --- FastAPI 应用实例 ---
app = FastAPI(title="Gemini Userscript Bridge API Server")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境建议限制来源
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# --- 服务器状态和 WebSocket 管理 ---
class ConnectionManager:
    def __init__(self):
        self.active_userscript: WebSocket | None = None
        self.userscript_api_key: str | None = None
        self.available_models: list[dict[str, Any]] = []
        self.pending_api_requests: dict[str, asyncio.Future[Any]] = {}
        self.pending_api_streams: dict[
            str, asyncio.Queue[dict[str, Any] | None]
        ] = {}  # Queue item can be None to signal end

    async def connect_userscript(self, websocket: WebSocket):
        await websocket.accept()
        if self.active_userscript:
            logger.warning("新的 Userscript 连接请求，但已有活动连接。旧连接将被关闭。")
            try:
                # 告知旧连接被取代
                await self.active_userscript.close(code=4001, reason="New connection replaced this one")
            except Exception as e:
                logger.info(f"关闭旧 Userscript 连接时出错: {e}")
        self.active_userscript = websocket
        logger.info("Userscript 已连接")

    def disconnect_userscript(self, websocket: WebSocket | None = None):
        # 只有当断开的是当前活动的 userscript 时才进行清理
        if websocket is None or self.active_userscript == websocket:
            logger.info("Userscript 已断开")
            self.active_userscript = None
            # 清理挂起的请求和流
            for future in self.pending_api_requests.values():
                if not future.done():
                    future.set_exception(
                        HTTPException(status_code=503, detail="Userscript disconnected during request")
                    )
            self.pending_api_requests.clear()

            for queue in self.pending_api_streams.values():
                queue.put_nowait(None)  # 发送 None 作为结束信号
            self.pending_api_streams.clear()
            self.available_models = []  # 清空可用模型
        else:
            logger.info("一个非活动的 Userscript 连接已断开。")

    async def send_to_userscript(self, message: dict[str, Any]):
        if self.active_userscript and self.active_userscript.client_state == WebSocketState.CONNECTED:
            try:
                await self.active_userscript.send_json(message)
            except WebSocketDisconnect:
                self.disconnect_userscript(self.active_userscript)  # 传入当前 websocket
                raise HTTPException(status_code=503, detail="Userscript disconnected before message could be sent")
            except Exception as e:
                logger.error(f"发送消息到 Userscript 失败: {e}")
                self.disconnect_userscript(self.active_userscript)
                raise HTTPException(status_code=500, detail=f"Failed to send message to Userscript: {e}")
        else:
            logger.error("尝试发送消息，但没有活动的 Userscript 连接或连接已关闭")
            raise HTTPException(status_code=503, detail="Userscript not connected or connection closed")

    def set_userscript_info(self, api_key: str | None, models: list[dict[str, Any]]):
        if api_key:  # 只有当 apiKey 存在时才更新
            self.userscript_api_key = api_key
            logger.info(f"Userscript API Key 已设置/更新 (前5位): {api_key[:5]}...")
        self.available_models = models
        logger.info(f"从 Userscript 收到模型列表: {[m.get('id', 'N/A') for m in models]}")


manager = ConnectionManager()
