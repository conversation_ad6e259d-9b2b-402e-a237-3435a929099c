
# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

node_modules
dist
dist-ssr
*.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
# Python
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
env/
venv/
.venv/
build/
dist/
*.egg-info/
.mypy_cache/
.pytest_cache/
.ruff_cache/

# FastAPI/Uvicorn
.uvicorn-test-socket

# UserScript
# Tampermonkey stores scripts internally, no files to ignore usually

# IDEs
.vscode/

# OS
.DS_Store
Thumbs.db
/references
/prompts
/memory-bank