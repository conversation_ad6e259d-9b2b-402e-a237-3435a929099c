/**
 * AI Studio 2 API - DOM Controller
 *
 * 控制 AI Studio 页面的 DOM 元素操作
 */

import type { UIElements, ModelInfo, GenerationConfig } from '../types';
import { DOM_SELECTORS, FALLBACK_SELECTORS, TIMEOUTS } from '../config/constants';
import {
  logger,
  waitForElements,
  isElementVisible,
  isElementInteractable,
  simulateInput,
  simulateClick,
  simulateEvent,
  delay
} from '../utils/helpers';
import { DOMError } from '../types';

export class DOMController {
  private elements: UIElements = {};
  private isInitialized = false;
  private modelList: ModelInfo[] = [];

  constructor() {
    this.bindMethods();
  }

  /**
   * 绑定方法到实例
   */
  private bindMethods(): void {
    this.findElement = this.findElement.bind(this);
    this.findElements = this.findElements.bind(this);
    this.waitForPageReady = this.waitForPageReady.bind(this);
  }

  /**
   * 初始化 DOM 控制器
   */
  public async initialize(): Promise<void> {
    try {
      logger.info('初始化 DOM 控制器...');

      await this.waitForPageReady();
      await this.findAllElements();

      this.isInitialized = true;
      logger.info('DOM 控制器初始化完成');
    } catch (error) {
      throw new DOMError(
        `DOM 控制器初始化失败: ${error instanceof Error ? error.message : String(error)}`,
        { originalError: error }
      );
    }
  }

  /**
   * 等待页面准备就绪
   */
  public async waitForPageReady(): Promise<void> {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('页面加载超时'));
      }, TIMEOUTS.DOM_READY_TIMEOUT);

      const checkReady = () => {
        if (document.readyState === 'complete' || document.readyState === 'interactive') {
          clearTimeout(timeout);
          resolve();
        } else {
          setTimeout(checkReady, 100);
        }
      };

      if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
          clearTimeout(timeout);
          resolve();
        }, { once: true });
      } else {
        checkReady();
      }
    });
  }

  /**
   * 查找单个元素
   */
  public async findElement(
    selector: string,
    fallbacks: string[] = [],
    timeout: number = TIMEOUTS.ELEMENT_WAIT
  ): Promise<Element> {
    const selectors = [selector, ...fallbacks];

    try {
      return await waitForElements(selectors, timeout);
    } catch (error) {
      throw new DOMError(
        `未找到元素: ${selector}`,
        { selector, fallbacks, timeout }
      );
    }
  }

  /**
   * 查找多个元素
   */
  public findElements(selector: string): NodeListOf<Element> {
    return document.querySelectorAll(selector);
  }

  /**
   * 查找所有必要的元素
   */
  private async findAllElements(): Promise<void> {
    try {
      // 查找输入框
      this.elements.promptInput = await this.findElement(
        DOM_SELECTORS.promptInput,
        [...FALLBACK_SELECTORS.promptInput]
      ) as HTMLTextAreaElement;

      // 查找提交按钮
      this.elements.submitButton = await this.findElement(
        DOM_SELECTORS.submitButton,
        [...FALLBACK_SELECTORS.submitButton]
      ) as HTMLButtonElement;

      // 查找响应容器（可选）
      try {
        this.elements.responseContainer = await this.findElement(
          DOM_SELECTORS.responseContainer,
          [],
          3000
        ) as HTMLElement;
      } catch {
        logger.warn('未找到响应容器，将在需要时重新查找');
      }

      // 查找模型选择器（可选）
      try {
        this.elements.modelSelector = await this.findElement(
          DOM_SELECTORS.modelSelector,
          FALLBACK_SELECTORS.modelSelector,
          3000
        ) as HTMLSelectElement;
      } catch {
        logger.warn('未找到模型选择器，模型切换功能可能不可用');
      }

      logger.info('成功找到主要 DOM 元素');
    } catch (error) {
      throw new DOMError(
        `查找 DOM 元素失败: ${error instanceof Error ? error.message : String(error)}`,
        { originalError: error }
      );
    }
  }

  /**
   * 发送消息到 AI Studio
   */
  public async sendMessage(message: string): Promise<void> {
    if (!this.isInitialized) {
      throw new DOMError('DOM 控制器未初始化');
    }

    if (!this.elements.promptInput) {
      throw new DOMError('未找到输入框元素');
    }

    if (!isElementInteractable(this.elements.promptInput)) {
      throw new DOMError('输入框不可交互');
    }

    try {
      logger.info(`发送消息: ${message.substring(0, 50)}...`);

      // 清空输入框并输入新消息
      if (this.elements.promptInput instanceof HTMLTextAreaElement ||
        this.elements.promptInput instanceof HTMLInputElement) {
        simulateInput(this.elements.promptInput, message);
      } else if (this.elements.promptInput.contentEditable === 'true') {
        this.elements.promptInput.textContent = message;
        simulateEvent(this.elements.promptInput, 'input');
      } else {
        throw new DOMError('不支持的输入框类型');
      }

      await delay(100); // 等待输入处理

      // 点击提交按钮
      await this.submitMessage();

      logger.info('消息发送成功');
    } catch (error) {
      throw new DOMError(
        `发送消息失败: ${error instanceof Error ? error.message : String(error)}`,
        { message, originalError: error }
      );
    }
  }

  /**
   * 提交消息
   */
  public async submitMessage(): Promise<void> {
    if (!this.elements.submitButton) {
      throw new DOMError('未找到提交按钮');
    }

    if (!isElementInteractable(this.elements.submitButton)) {
      throw new DOMError('提交按钮不可交互');
    }

    try {
      simulateClick(this.elements.submitButton);
      await delay(200); // 等待提交处理
    } catch (error) {
      throw new DOMError(
        `提交消息失败: ${error instanceof Error ? error.message : String(error)}`,
        { originalError: error }
      );
    }
  }

  /**
   * 切换模型
   */
  public async switchModel(modelId: string): Promise<void> {
    if (!this.modelList.length) {
      throw new DOMError('模型列表未加载');
    }

    const model = this.modelList.find(m => m.id === modelId || m.name === modelId);
    if (!model) {
      throw new DOMError(`未找到模型: ${modelId}`);
    }

    try {
      logger.info(`切换到模型: ${model.displayName} (${model.id})`);

      // 点击模型选择器
      if (!this.elements.modelSelector) {
        this.elements.modelSelector = await this.findElement(
          DOM_SELECTORS.modelSelector,
          FALLBACK_SELECTORS.modelSelector
        ) as HTMLSelectElement;
      }

      simulateClick(this.elements.modelSelector);
      await delay(TIMEOUTS.MODEL_SWITCH_DELAY);

      // 查找并点击目标模型选项
      const optionSelector = `mat-option, div[role="option"]`;
      const options = this.findElements(optionSelector);

      let targetOption: Element | null = null;
      for (const option of options) {
        const text = option.textContent?.trim() || '';
        if (text.includes(model.displayName) || text.includes(model.name)) {
          targetOption = option;
          break;
        }
      }

      if (!targetOption) {
        throw new DOMError(`未找到模型选项: ${model.displayName}`);
      }

      simulateClick(targetOption);
      await delay(TIMEOUTS.PARAMETER_SET_DELAY);

      logger.info(`模型切换成功: ${model.displayName}`);
    } catch (error) {
      throw new DOMError(
        `模型切换失败: ${error instanceof Error ? error.message : String(error)}`,
        { modelId, model, originalError: error }
      );
    }
  }

  /**
   * 设置生成参数
   */
  public async setGenerationConfig(config: GenerationConfig): Promise<void> {
    try {
      logger.info('设置生成参数:', config);

      if (config.temperature !== undefined) {
        await this.setTemperature(config.temperature);
      }

      if (config.topP !== undefined) {
        await this.setTopP(config.topP);
      }

      if (config.maxOutputTokens !== undefined) {
        await this.setMaxTokens(config.maxOutputTokens);
      }

      if (config.stopSequences !== undefined) {
        await this.setStopSequences(config.stopSequences);
      }

      logger.info('生成参数设置完成');
    } catch (error) {
      throw new DOMError(
        `设置生成参数失败: ${error instanceof Error ? error.message : String(error)}`,
        { config, originalError: error }
      );
    }
  }

  /**
   * 设置温度参数
   */
  private async setTemperature(temperature: number): Promise<void> {
    try {
      const input = await this.findElement(DOM_SELECTORS.temperatureInput) as HTMLInputElement;
      simulateInput(input, temperature.toString());
      await delay(TIMEOUTS.PARAMETER_SET_DELAY);
    } catch (error) {
      logger.warn('设置温度参数失败:', error);
    }
  }

  /**
   * 设置 Top P 参数
   */
  private async setTopP(topP: number): Promise<void> {
    try {
      const input = await this.findElement(DOM_SELECTORS.topPInput) as HTMLInputElement;
      simulateInput(input, topP.toString());
      await delay(TIMEOUTS.PARAMETER_SET_DELAY);
    } catch (error) {
      logger.warn('设置 Top P 参数失败:', error);
    }
  }

  /**
   * 设置最大输出令牌数
   */
  private async setMaxTokens(maxTokens: number): Promise<void> {
    try {
      const input = await this.findElement(DOM_SELECTORS.maxTokensInput) as HTMLInputElement;
      simulateInput(input, maxTokens.toString());
      await delay(TIMEOUTS.PARAMETER_SET_DELAY);
    } catch (error) {
      logger.warn('设置最大令牌数失败:', error);
    }
  }

  /**
   * 设置停止序列
   */
  private async setStopSequences(stopSequences: string[]): Promise<void> {
    try {
      const input = await this.findElement(DOM_SELECTORS.stopSequenceInput) as HTMLInputElement;

      // 清除现有的停止序列
      const removeButtons = this.findElements('mat-chip-row button[aria-label*="Remove"]');
      for (const button of removeButtons) {
        simulateClick(button);
        await delay(50);
      }

      // 添加新的停止序列
      for (const sequence of stopSequences) {
        if (sequence.trim()) {
          simulateInput(input, sequence.trim());
          simulateEvent(input, 'keydown', { key: 'Enter', code: 'Enter' });
          await delay(100);
        }
      }
    } catch (error) {
      logger.warn('设置停止序列失败:', error);
    }
  }

  /**
   * 获取响应文本
   */
  public async getResponseText(): Promise<string> {
    try {
      if (!this.elements.responseContainer) {
        this.elements.responseContainer = await this.findElement(
          DOM_SELECTORS.responseContainer
        ) as HTMLElement;
      }

      const textElement = this.elements.responseContainer.querySelector(DOM_SELECTORS.responseText);
      if (!textElement) {
        return '';
      }

      return textElement.textContent?.trim() || '';
    } catch (error) {
      logger.warn('获取响应文本失败:', error);
      return '';
    }
  }

  /**
   * 检查是否正在生成
   */
  public isGenerating(): boolean {
    try {
      const spinner = document.querySelector(DOM_SELECTORS.loadingSpinner);
      return spinner !== null && isElementVisible(spinner);
    } catch {
      return false;
    }
  }

  /**
   * 等待生成完成
   */
  public async waitForGenerationComplete(): Promise<void> {
    const startTime = Date.now();

    while (this.isGenerating()) {
      if (Date.now() - startTime > TIMEOUTS.RESPONSE_TIMEOUT) {
        throw new DOMError('等待生成完成超时');
      }

      await delay(TIMEOUTS.RESPONSE_POLL_INTERVAL);
    }
  }

  /**
   * 设置模型列表
   */
  public setModelList(models: ModelInfo[]): void {
    this.modelList = models;
    logger.debug(`设置模型列表: ${models.length} 个模型`);
  }

  /**
   * 获取当前页面状态
   */
  public getPageState(): { isReady: boolean; hasInput: boolean; isGenerating: boolean } {
    return {
      isReady: this.isInitialized,
      hasInput: !!this.elements.promptInput && isElementInteractable(this.elements.promptInput),
      isGenerating: this.isGenerating(),
    };
  }

  /**
   * 刷新元素引用
   */
  public async refreshElements(): Promise<void> {
    this.elements = {};
    await this.findAllElements();
  }
}
