import { defineConfig } from 'vite';
import monkey from 'vite-plugin-monkey';


// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    monkey({
      entry: "src/main.ts",
      userscript: {
        name: {
          en: "AI-studio web to api",
          zh: "AI-studio 网页转 API",
        },
        namespace: "https://github.com/lll9p/gemini2api",
        version: "0.0.1",
        icon: "https://vitejs.dev/logo.svg",
        description: {
          "": "A userscript to convert AI-studio web pages to API.",
          zh: "一个将 AI-studio 网页转换为 API 的用户脚本。",
          en: "A userscript to convert AI-studio web pages to API.",
        },
        match: ["https://aistudio.google.com/*"],
      },
    }),
  ],
});